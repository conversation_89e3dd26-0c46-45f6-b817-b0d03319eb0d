/**
 * API服务模块
 * 负责与GoMyHire API的所有交互，包括认证、数据获取、订单创建等
 * 重构为传统script标签加载方式
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块（延迟获取以确保加载顺序）
    function getAppState() {
        return window.OTA.appState || window.appState;
    }

    function getLogger() {
        return window.OTA.logger || window.logger;
    }

    // 新增：获取GeminiService 实例
    function getGeminiService() {
        var service = window.OTA.geminiService || window.geminiService; // 获取全局Gemini服务实例
        if (service) {
            service.setRealtimeAnalysis = function(config) {
                // 如果传入值为 boolean，则调整为配置对象
                if (typeof config === 'boolean') {
                    this.configureRealtimeAnalysis({ enabled: config });
                } else {
                    this.configureRealtimeAnalysis(config);
                }
            };
        }
        return service;
    }

class ApiService {
    constructor() {
        this.baseURL = 'https://gomyhire.com.my/api';
        this.timeout = 30000; // 30秒超时
        
        // 更新的静态映射数据（基于最新的API文档 api return id list.md）
        this.staticData = {
            backendUsers: [
                { id: 1, name: 'Super Admin', email: '', phone: '', role_id: 1 },
                { id: 37, name: 'smw', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 89, name: 'GMH Sabah', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 310, name: 'Jcy', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 311, name: 'opAnnie', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 312, name: 'opVenus', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 313, name: 'opEric', email: '', phone: '', role_id: 2 },
                { id: 342, name: 'SMW Wendy', email: 'SMW <EMAIL>', phone: '', role_id: 2 },
                { id: 343, name: 'SMW XiaoYu', email: 'SMW <EMAIL>', phone: '', role_id: 2 },
                { id: 420, name: 'chongyoonlim', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 421, name: 'josua', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 428, name: 'Gomyhire Yong', email: '', phone: '', role_id: 2 },
                { id: 533, name: 'xhs', email: '', phone: '', role_id: 2 },
                { id: 622, name: 'CsBob', email: '', phone: '', role_id: 2 },
                { id: 777, name: '空空', email: '空空@gomyhire.com', phone: '', role_id: 2 },
                { id: 812, name: '淼淼', email: '', phone: '', role_id: 2 },
                { id: 856, name: 'GMH Ashley', email: '', phone: '', role_id: 2 },
                { id: 907, name: 'OP XINYIN', email: '', phone: '', role_id: 2 },
                { id: 1043, name: 'Billy Yong close', email: '', phone: '', role_id: 2 },
                { id: 1047, name: 'OP QiJun', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 1181, name: 'Op Karen', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 1201, name: 'KK Lucas', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 1223, name: 'Chong admin', email: '', phone: '', role_id: 2 },
                { id: 1652, name: 'CSteam Swee Qing', email: 'Swee <EMAIL>', phone: '', role_id: 2 },
                { id: 1832, name: 'GMH SG William', email: '', phone: '', role_id: 2 },
                { id: 2050, name: 'agent victor', email: '', phone: '', role_id: 2 },
                { id: 2085, name: 'CSteam Tze Ying', email: '', phone: '', role_id: 2 },
                { id: 2141, name: 'SMW Nas', email: '', phone: '', role_id: 2 },
                { id: 2142, name: 'SMW Wen', email: '', phone: '', role_id: 2 },
                { id: 2248, name: 'GMH Shi Wei', email: '', phone: '', role_id: 2 },
                { id: 2249, name: 'Skymirror jetty', email: 'Skymirror <EMAIL>', phone: '', role_id: 2 },
                { id: 2340, name: 'GMH JingSoon', email: '', phone: '', role_id: 2 },
                { id: 2358, name: 'GMH Zilok', email: '', phone: '', role_id: 2 },
                { id: 2446, name: 'UCSI - Cheras', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 2503, name: 'GMH Veron', email: '', phone: '', role_id: 2 },
                { id: 2666, name: 'JRCoach', email: '<EMAIL>', phone: '', role_id: 2 },
                { id: 2595, name: 'Admin Pua', email: '', phone: '', role_id: 2 },
                { id: 550, name: 'test', email: '', phone: '', role_id: 2 }
            ],
            // 确保子分类只包含接机，送机，包车
            subCategories: [
                { id: 2, name: 'Pickup' },
                { id: 3, name: 'Dropoff' },
                { id: 4, name: 'Charter' }
            ],
            carTypes: [
                { id: 38, name: '4 Seater Hatchback (3 passenger, 2 x L size luggage)', passengerLimit: 3 },
                { id: 5, name: '5 Seater (3 passenger, 3 x L size luggage)', passengerLimit: 3 },
                { id: 33, name: 'Premium 5 Seater (Mercedes/BMW Only) (3 passenger, 3 x L size luggage)', passengerLimit: 3 },
                { id: 37, name: 'Extended 5 (4 passenger, 4 x L size luggage)', passengerLimit: 4 },
                { id: 35, name: '7 Seater SUV (4 passenger, 4 x L size luggage)', passengerLimit: 4 },
                { id: 15, name: '7 Seater MPV (5 passenger, 4 x L size luggage)', passengerLimit: 5 },
                { id: 16, name: 'Standard Size MPV (5 passenger, 4 x L size luggage)', passengerLimit: 5 },
                { id: 31, name: 'Luxury Mpv (Serena) (5 passenger, 4 x L size luggage)', passengerLimit: 5 },
                { id: 32, name: 'Velfire/ Alphard (6 passenger, 4 x L size luggage)', passengerLimit: 6 },
                { id: 36, name: 'Alphard (6 passenger, 4 x L size luggage)', passengerLimit: 6 },
                { id: 20, name: '10 Seater MPV / Van (7 passenger, 7 x L size luggage)', passengerLimit: 7 },
                { id: 30, name: '12 seat Starex (7 passenger, 7 x L size luggage)', passengerLimit: 7 },
                { id: 23, name: '14 Seater Van (10 passenger, 10 x L size luggage)', passengerLimit: 10 },
                { id: 24, name: '18 Seater Van (12 passenger, 12 x L size luggage)', passengerLimit: 12 },
                { id: 25, name: '30 Seat Mni Bus (29 passenger, 29 x L size luggage)', passengerLimit: 29 },
                { id: 26, name: '44 Seater Bus (43 passenger, 43 x L size luggage)', passengerLimit: 43 },
                { id: 34, name: 'Ticket (N/A passenger, N/A luggage)', passengerLimit: 0 },
                { id: 39, name: 'Ticket (Non-Malaysian) (N/A passenger, N/A luggage)', passengerLimit: 0 }
            ],
            drivingRegions: [
                { id: 1, name: 'Kl/selangor (KL)' },
                { id: 2, name: 'Penang (PNG)' },
                { id: 3, name: 'Johor (JB)' },
                { id: 4, name: 'Sabah (SBH)' },
                { id: 5, name: 'Singapore (SG)' },
                { id: 6, name: '携程专车 (CTRIP)' },
                { id: 8, name: 'Complete (COMPLETE)' },
                { id: 9, name: 'Paging (PG)' },
                { id: 10, name: 'Charter (CHRT)' },
                { id: 12, name: 'Malacca (MLK)' },
                { id: 13, name: 'SMW (SMW)' }
            ],
            languages: [
                { id: 2, name: 'English (EN)' },
                { id: 3, name: 'Malay (MY)' },
                { id: 4, name: 'Chinese (CN)' },
                { id: 5, name: 'Paging (PG)' },
                { id: 6, name: 'Charter (CHARTER)' },
                { id: 8, name: '携程司导 (IM)' },
                { id: 9, name: 'PSV (PSV)' },
                { id: 10, name: 'EVP (EVP)' },
                { id: 11, name: 'Car Type Reverify (CAR)' },
                { id: 12, name: 'Jetty (JETTY)' },
                { id: 13, name: 'PhotoSkill Proof (PHOTO)' }
            ],
            lastUpdated: null // 首次加载时设置为当前时间
        };
    }
    
    /**
     * 发送HTTP请求的通用方法
     * @param {string} url - 请求URL
     * @param {object} options - 请求选项
     * @returns {Promise<object>} 响应数据
     */
    async request(url, options = {}) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);
        const startTime = Date.now();
        
        try {
            const config = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                signal: controller.signal,
                ...options
            };
            
            // 添加认证头
            const token = getAppState().get('auth.token');
            if (token && !options.skipAuth) {
                config.headers['Authorization'] = `Bearer ${token}`;
            }
            
            getLogger().log('API请求开始', 'info', {
                url,
                method: config.method,
                hasAuth: !!config.headers['Authorization']
            });
            
            const response = await fetch(url, config);
            const duration = Date.now() - startTime;
            clearTimeout(timeoutId);
            
            let data;
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                data = await response.json();
            } else {
                data = await response.text();
            }
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            getLogger().logApiCall(url, config.method, options.body, data, duration);
            getAppState().recordApiCall({ url, method: config.method, status: response.status });
            
            return data;
            
        } catch (error) {
            const duration = Date.now() - startTime;
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                throw new Error('请求超时');
            }
            
            getLogger().logApiCall(url, options.method || 'GET', options.body, null, duration);
            getLogger().logError('API请求失败', { url, error: error.message });
            getAppState().addError({ type: 'api', url, message: error.message });
            
            throw error;
        }
    }
    
    /**
     * 用户登录
     * @param {string} email - 邮箱
     * @param {string} password - 密码
     * @param {boolean} rememberMe - 是否记住登录状态
     * @returns {Promise<object>} 登录结果
     */
    async login(email, password, rememberMe = false) {
        try {
            getLogger().logUserAction('尝试登录', { email, rememberMe });

            const data = await this.request(`${this.baseURL}/login`, {
                method: 'POST',
                body: JSON.stringify({ email, password }),
                skipAuth: true
            });

            if (data.status && data.token) {
                // 处理token格式 - API返回格式为 "2409|F0hye4nKqL58qhcdW8knwLwLMbQltjl35uggBwGA"
                // 需要提取 "|" 后面的实际token部分
                let actualToken = data.token;
                if (data.token.includes('|')) {
                    actualToken = data.token.split('|')[1];
                }

                // 构建用户信息对象
                const user = {
                    email: email,
                    name: email.split('@')[0], // 从邮箱提取用户名作为显示名称
                    loginTime: new Date().toISOString()
                };

                // 保存认证状态到AppState
                getAppState().setAuth(actualToken, user, rememberMe);

                // 记录登录成功日志
                getLogger().logUserAction('登录成功', {
                    email,
                    rememberMe,
                    tokenLength: actualToken.length
                });

                // 返回成功结果给调用者
                return {
                    success: true,
                    message: '登录成功',
                    user: user,
                    token: actualToken
                };
            } else {
                throw new Error(data.message || '登录失败');
            }
        } catch (error) {
            getLogger().logUserAction('登录失败', { email, error: error.message });
            throw error;
        }
    }

    /**
     * 用户退出登录
     * @description 清除前端认证状态并刷新页面。
     */
    logout() {
        const appState = getAppState(); // 获取 appState 实例
        getLogger().logUserAction('用户登出'); // 记录用户操作日志
        appState.clearAuth(true); // 清除认证信息，但保留“记住我”的设置
        window.location.reload(); // 刷新页面以完成登出
    }
    
    /**
     * 获取后台用户列表（只用本地静态数据，不请求API）
     */
    async getBackendUsers(search = '') {
        // 只返回本地静态数据
        return this.staticData.backendUsers;
    }
    
    /**
     * 获取子分类列表（只用本地静态数据，不请求API）
     */
    async getSubCategories(search = '') {
        return this.staticData.subCategories;
    }
    
    /**
     * 获取车型列表（只用本地静态数据，不请求API）
     */
    async getCarTypes(search = '') {
        return this.staticData.carTypes;
    }
    
    /**
     * 获取行驶区域列表（只用本地静态数据，不请求API）
     */
    async getDrivingRegions(search = '') {
        return this.staticData.drivingRegions;
    }
    
    /**
     * 获取语言列表（只用本地静态数据，不请求API）
     */
    async getLanguages(search = '') {
        return this.staticData.languages;
    }
    
    /**
     * 获取所有系统数据（后台用户、子分类、车型、区域、语言）
     * @description 使用 Promise.all 并行获取所有核心数据，并包含完整的错误处理。
     *              这确保了应用程序在所有必要数据加载完成后再继续执行，解决了启动挂起的问题。
     * @returns {Promise<object>} 包含所有系统数据的对象
     */
    async getAllSystemData() {
        const appState = getAppState(); // 获取 AppState 实例
        const logger = getLogger(); // 获取 Logger 实例
        const geminiService = getGeminiService(); // 获取 Gemini 服务实例

        logger.log('开始获取所有系统数据...', 'info'); // 记录日志

        try {
            // 使用 Promise.all 并行执行所有数据获取的 Promise
            const [
                backendUsers,
                subCategories,
                carTypes,
                drivingRegions,
                languages
            ] = await Promise.all([
                this.getBackendUsers(),
                this.getSubCategories(),
                this.getCarTypes(),
                this.getDrivingRegions(),
                this.getLanguages()
            ]);

            // 将获取到的数据整合为一个 systemData 对象
            const systemData = {
                backendUsers,
                subCategories,
                carTypes,
                drivingRegions,
                languages
            };

            logger.log('所有系统数据已成功获取', 'success', { // 记录成功获取的日志
                userCount: backendUsers.length,
                carTypeCount: carTypes.length,
                subCategoryCount: subCategories.length
            });

            // 将整合后的数据设置到全局应用状态中
            appState.setSystemData(systemData);

            // 如果 Gemini 服务可用，则同步更新其内部的 ID 映射
            if (geminiService && typeof geminiService.updateIdMappings === 'function') {
                geminiService.updateIdMappings(systemData);
            }

            // 返回整合后的系统数据
            return systemData;

        } catch (error) {
            // 如果在获取数据的过程中发生任何错误
            logger.logError('获取系统数据时发生严重错误', { // 记录严重错误日志
                error: error.message,
                stack: error.stack
            });
            // 抛出错误，中断应用程序的初始化流程，以便上层代码可以捕获并处理
            throw new Error(`系统数据加载失败: ${error.message}`);
        }
    }
    
    /**
     * 创建订单
     * @param {object} orderData - 订单数据
     * @returns {Promise<object>} 创建结果
     */
    async createOrder(orderData) {
        try {
            getLogger().logUserAction('开始创建订单', { orderData });
            
            // 预处理订单数据
            const processedData = this.preprocessOrderData(orderData);
            
            const data = await this.request(`${this.baseURL}/create_order`, {
                method: 'POST',
                body: JSON.stringify(processedData),
                skipAuth: true // 创建订单API不需要认证
            });
            
            if (data.status === false && data.data?.validation_error) {
                // 验证错误
                getLogger().log('订单验证失败', 'warning', { errors: data.data.validation_error });
                return { success: false, errors: data.data.validation_error, message: data.message };
            } else if (data.status === true) {
                // 创建成功
                getLogger().logUserAction('订单创建成功', { orderId: data.data?.id });
                return { success: true, data: data.data };
            } else {
                // 其他错误
                throw new Error(data.message || '订单创建失败');
            }
            
        } catch (error) {
            getLogger().logError('订单创建异常', { error: error.message, orderData });
            throw error;
        }
    }
    
    /**
     * 预处理订单数据
     * @param {object} orderData - 原始订单数据
     * @returns {object} 处理后的订单数据
     */
    preprocessOrderData(orderData) {
        const processed = { ...orderData };
        
        // 直接保持 YYYY-MM-DD，不再转换为 DD-MM-YYYY
        // if (processed.date) {
        //     const dateParts = processed.date.split('-');
        //     if (dateParts.length === 3) {
        //         processed.date = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;
        //     }
        // }
        
        // 处理语言数组格式 - 使用对象格式避免HTTP 500错误
        if (processed.languages_id_array && Array.isArray(processed.languages_id_array)) {
            // 转换为对象格式 {"0":"1","1":"2","2":"3"}
            const languageObject = {};
            processed.languages_id_array.forEach((id, index) => {
                languageObject[index.toString()] = id.toString();
            });
            processed.languages_id_array = languageObject;
        }
        
        // 确保必需字段为字符串
        ['sub_category_id', 'car_type_id', 'incharge_by_backend_user_id'].forEach(field => {
            if (processed[field] !== undefined) {
                processed[field] = String(processed[field]);
            }
        });
        
        // 移除空值字段
        Object.keys(processed).forEach(key => {
            if (processed[key] === '' || processed[key] === null || processed[key] === undefined) {
                delete processed[key];
            }
        });
        
        getLogger().log('订单数据预处理完成', 'info', { processed });
        return processed;
    }
    
    /**
     * 根据乘客人数推荐车型（优化版本，默认使用Comfort 5 Seater）
     * @param {number} passengerCount - 乘客人数
     * @returns {number} 推荐的车型ID
     */
    recommendCarType(passengerCount) {
        // 如果没有乘客人数信息，默认使用5 Seater (ID: 5)
        if (!passengerCount || isNaN(parseInt(passengerCount))) {
            getLogger().log('无乘客人数信息，使用默认车型5 Seater', 'info', {
                defaultCarTypeId: 5
            });
            return 5;
        }

        const count = parseInt(passengerCount);

        // 使用与GeminiService一致的车型映射，但优先使用5 Seater
        const carTypeMapping = [
            { id: 5, name: '5 Seater', passengerLimit: 3 },
            { id: 15, name: '7 Seater MPV', passengerLimit: 5 },
            { id: 20, name: '10 Seater MPV / Van', passengerLimit: 7 },
            { id: 23, name: '14 Seater Van', passengerLimit: 10 },
            { id: 24, name: '18 Seater Van', passengerLimit: 12 },
            { id: 25, name: '30 Seat Mini Bus', passengerLimit: 29 },
            { id: 26, name: '44 Seater Bus', passengerLimit: 43 }
        ];

        for (const carType of carTypeMapping) {
            if (count <= carType.passengerLimit) {
                getLogger().log('车型推荐', 'info', {
                    passengerCount: count,
                    recommended: carType
                });
                return carType.id;
            }
        }

        // 默认返回最大车型
        getLogger().log('使用最大车型', 'warning', { passengerCount: count });
        return 23;
    }

    /**
     * 获取默认后台用户ID（基于当前登录用户和系统数据）
     * @returns {number} 默认后台用户ID
     */
    getDefaultBackendUserId() {
        const currentUser = getAppState().get('auth.user');

        // 优先在系统数据中匹配邮箱 → ID （实时、无需硬编码）
        if (currentUser && currentUser.email) {
            const backendUsers = getAppState().get('systemData.backendUsers') || [];
            const matched = backendUsers.find(u => (u.email || '').toLowerCase() === currentUser.email.toLowerCase());
            if (matched) {
                getLogger().log('根据登录邮箱匹配到后台用户', 'info', {
                    email: currentUser.email,
                    backendUserId: matched.id
                });
                return matched.id;
            }
        }

        // 若无邮件匹配，退回旧映射表（少量硬编码）
        const userMapping = {
            '<EMAIL>': 37,
            '<EMAIL>': 310,
            '<EMAIL>': 1
        };

        if (currentUser && currentUser.email && userMapping[currentUser.email]) {
            const mappedId = userMapping[currentUser.email];
            getLogger().log('使用硬编码映射的后台用户', 'info', {
                email: currentUser.email,
                backendUserId: mappedId
            });
            return mappedId;
        }

        // 否则使用系统数据中的第一个后台用户
        const backendUsers = getAppState().get('systemData.backendUsers') || [];
        if (backendUsers.length > 0) {
            const firstUserId = backendUsers[0].id;
            getLogger().log('使用系统数据中的第一个后台用户', 'info', {
                backendUserId: firstUserId,
                userName: backendUsers[0].name
            });
            return firstUserId;
        }

        // 最后的默认值
        getLogger().log('使用默认后台用户ID', 'warning', { defaultId: 1 });
        return 1;
    }

    /**
     * 获取默认语言数组（基于客户姓名自动检测）
     * @param {string} customerName - 客户姓名
     * @returns {object} 语言数组对象格式
     */
    getDefaultLanguagesArray(customerName = '') {
        // 检测中文字符的正则表达式
        const chineseRegex = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/;

        let languageId = 2; // 默认English (ID: 2)

        // 如果客户姓名包含中文字符，选择Chinese
        if (customerName && chineseRegex.test(customerName)) {
            languageId = 4; // Chinese (ID: 4)
            getLogger().log('检测到中文姓名，自动选择中文语言', 'info', {
                customerName,
                selectedLanguageId: languageId
            });
        } else {
            getLogger().log('使用默认英文语言', 'info', {
                customerName: customerName || '无姓名',
                selectedLanguageId: languageId
            });
        }

        // 返回对象格式以确保GoMyHire API兼容性
        return {
            "0": languageId.toString()
        };
    }

    /**
     * 获取允许的子分类列表（限制范围）
     * @returns {Array} 允许的子分类列表
     */
    getAllowedSubCategories() {
        return this.staticData.subCategories;
    }

    /**
     * 验证子分类ID是否在允许范围内
     * @param {number} subCategoryId - 子分类ID
     * @returns {boolean} 是否允许
     */
    isAllowedSubCategory(subCategoryId) {
        const allowedIds = [2, 3, 4]; // Pickup, Dropoff, Charter
        return allowedIds.includes(parseInt(subCategoryId));
    }
    
    /**
     * 验证订单数据
     * @param {object} orderData - 订单数据
     * @returns {object} 验证结果
     */
    validateOrderData(orderData) {
        const errors = {};
        const warnings = [];
        
        // 必需字段验证
        const requiredFields = [
            { field: 'sub_category_id', name: '子分类' },
            { field: 'ota_reference_number', name: 'OTA参考号' },
            { field: 'car_type_id', name: '车型' },
            { field: 'incharge_by_backend_user_id', name: '负责人' }
        ];
        
        requiredFields.forEach(({ field, name }) => {
            if (!orderData[field]) {
                errors[field] = [`${name}为必填项`];
            }
        });
        
        // 邮箱格式验证
        if (orderData.customer_email && !this.isValidEmail(orderData.customer_email)) {
            errors.customer_email = ['邮箱格式不正确'];
        }
        
        // 电话格式验证
        if (orderData.customer_contact && !this.isValidPhone(orderData.customer_contact)) {
            warnings.push('电话号码格式可能不正确');
        }
        
        // 日期验证
        if (orderData.date && !this.isValidDate(orderData.date)) {
            errors.date = ['日期格式不正确'];
        }
        
        // 数字字段验证
        ['passenger_number', 'luggage_number', 'ota_price', 'driver_fee', 'driver_collect'].forEach(field => {
            if (orderData[field] !== undefined && isNaN(Number(orderData[field]))) {
                errors[field] = [`${field}必须是数字`];
            }
        });
        
        const result = {
            isValid: Object.keys(errors).length === 0,
            errors,
            warnings
        };
        
        getLogger().logDataChange('订单验证', null, result, 'validation');
        return result;
    }
    
    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @returns {boolean} 是否有效
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    /**
     * 验证电话格式
     * @param {string} phone - 电话号码
     * @returns {boolean} 是否有效
     */
    isValidPhone(phone) {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
        return phoneRegex.test(phone);
    }
    
    /**
     * 验证日期格式
     * @param {string} date - 日期字符串
     * @returns {boolean} 是否有效
     */
    isValidDate(date) {
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(date)) return false;
        
        const dateObj = new Date(date);
        return dateObj instanceof Date && !isNaN(dateObj);
    }
    
    /**
     * 获取系统状态
     * @returns {object} 系统状态
     */
    getSystemStatus() {
        return {
            connected: getAppState().get('system.connected'),
            lastApiCall: getAppState().get('system.lastApiCall'),
            apiCallCount: getAppState().get('system.apiCallCount'),
            hasSystemData: getAppState().get('systemData.lastUpdated') !== null,
            authStatus: getAppState().get('auth.isLoggedIn')
        };
    }
}

    // 创建全局API服务实例
    const apiService = new ApiService();

    // 暴露到OTA命名空间
    window.OTA.apiService = apiService;

    // 向后兼容：暴露到全局window对象
    window.apiService = apiService;

})();